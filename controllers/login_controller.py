from flask import request, jsonify
from flask_jwt_extended import create_access_token
from datetime import timedelta
from app.extensions import mongo
from flask_security.utils import verify_password

def signup():
    data = request.get_json()
    email = data.get("email")
    password = data.get("password")

    user = mongo.db.users.find_one({"email": email})
    
    if not user.get("email_verified", False):
        return jsonify({"msg": "Please verify your email before logging in"}), 403

    if user.get("banned"):
        return jsonify({"msg": "Your account has been banned"}), 403

    if not user:
        return jsonify({"msg": "User not found"}), 404

    if not verify_password(password, user["password"]):
        return jsonify({"msg": "Invalid credentials"}), 401

    token = create_access_token(
        identity=user["username"],
        additional_claims={
            "id": str(user["_id"]),
            "email": user["email"],
            "username": user["username"],
            "role": user.get("role", "user")
        },
        expires_delta=timedelta(hours=2)
    )

    return jsonify({
        "msg": "Login successful",
        "token": token,
        "id": str(user["_id"]),
        "username": user["username"],
        "email": user["email"]
    }), 200
