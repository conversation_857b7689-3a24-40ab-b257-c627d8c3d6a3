from flask import request, jsonify
from extensions import mongo, mail
from utils.hash_utils import verify_password_custom
from flask_mail import Message
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from datetime import timedelta
import random

login_otp_store = {}

def login():
    data = request.get_json()
    email = data.get("email")
    password = data.get("password")

    user = mongo.db.users.find_one({"email": email})
    if not user:
        return jsonify({"msg": "User not found"}), 404
    if user.get("banned"):
        return jsonify({"msg": "Your account has been banned"}), 403
    if not user.get("email_verified", False):
        return jsonify({"msg": "Please verify your email before logging in"}), 403
    if not verify_password_custom(password, user["password"]):
        return jsonify({"msg": "Invalid credentials"}), 401

    if user.get("2fa_enabled", False):
        otp = str(random.randint(100000, 999999))
        login_otp_store[email] = {"otp": otp, "user": user}

        msg = Message(
            subject="PICA - Login Verification Code",
            sender="<EMAIL>",
            recipients=[email],
            body=f"Your login verification code is: {otp}"
        )
        mail.send(msg)

        return jsonify({"msg": "OTP sent to email", "2fa": True}), 200

    token = create_access_token(
        identity=user["username"],
        additional_claims={
            "id": str(user["_id"]),
            "username": user["username"],
            "email": user["email"],
            "role": user.get("role", "user")
        },
        expires_delta=timedelta(hours=2)
    )

    return jsonify({
        "msg": "Login successful",
        "token": token,
        "id": str(user["_id"]),
        "username": user["username"],
        "email": user["email"],
        "2fa": False
    }), 200

def verify_otp():
    data = request.get_json()
    email = data.get("email")
    otp = data.get("otp")

    record = login_otp_store.get(email)
    if not record:
        return jsonify({"msg": "No login in progress"}), 400
    if record["otp"] != otp:
        return jsonify({"msg": "Invalid OTP"}), 400

    user = record["user"]
    del login_otp_store[email]

    token = create_access_token(
        identity=user["username"],
        additional_claims={
            "id": str(user["_id"]),
            "username": user["username"],
            "email": user["email"],
            "role": user.get("role", "user")
        },
        expires_delta=timedelta(hours=2)
    )

    return jsonify({
        "msg": "Login successful",
        "token": token,
        "id": str(user["_id"]),
        "username": user["username"],
        "email": user["email"]
    }), 200

def toggle_2fa():
    user_email = get_jwt_identity()
    data = request.get_json()
    enable_2fa = data.get("enabled", False)

    result = mongo.db.users.update_one(
        {"email": user_email},
        {"$set": {"2fa_enabled": enable_2fa}}
    )

    if result.modified_count:
        return jsonify({"msg": f"2FA {'enabled' if enable_2fa else 'disabled'}"}), 200
    return jsonify({"msg": "No change made"}), 400
