"""
OpenVAS Controller Module

This module provides REST API endpoints for OpenVAS vulnerability scanning operations.
All endpoints are automatically managed with proper resource cleanup and error handling.
"""

import time
import logging
from typing import Dict, Any, Tuple, Optional
from flask import request, jsonify, current_app, Response
from services.openvas_service import OpenVASService
from functools import wraps

# Configure logging
logger = logging.getLogger(__name__)

# Constants
POLL_INTERVAL = 10  # seconds
MAX_CONSECUTIVE_ERRORS = 5
TASK_INIT_WAIT = 5  # seconds

def _get_openvas_service() -> OpenVASService:
    """
    Create OpenVAS service instance with configuration from Flask app.

    Returns:
        OpenVASScanService: Configured service instance

    Raises:
        KeyError: If required configuration is missing
    """
    try:
        return OpenVASService(
            current_app.config["OPENVAS_USERNAME"],
            current_app.config["OPENVAS_PASSWORD"],
            current_app.config["OPENVAS_SOCKET_PATH"]
        )
    except KeyError as e:
        logger.error(f"Missing OpenVAS configuration: {e}")
        raise

def with_openvas_service(func):
    """
    Decorator for automatic OpenVAS service lifecycle management.

    Features:
    - Automatic service creation and cleanup
    - Consistent error handling and logging
    - Resource leak prevention
    """
    @wraps(func)
    def wrapper(*args, **kwargs) -> Tuple[Response, int]:
        service = None
        try:
            service = _get_openvas_service()
            return func(service, *args, **kwargs)
        except Exception as e:
            logger.error(f"OpenVAS operation failed in {func.__name__}: {str(e)}")
            return jsonify({
                "error": f"OpenVAS operation failed: {str(e)}",
                "operation": func.__name__
            }), 500
        finally:
            if service:
                service.close()
    return wrapper

def _validate_required_fields(data: Dict[str, Any], required_fields: list) -> Optional[Tuple[Response, int]]:
    """
    Validate that all required fields are present in request data.

    Args:
        data: Request data dictionary
        required_fields: List of required field names

    Returns:
        Error response tuple if validation fails, None if valid
    """
    missing_fields = [field for field in required_fields if not data.get(field)]
    if missing_fields:
        return jsonify({
            "error": f"Missing required fields: {', '.join(missing_fields)}",
            "required_fields": required_fields,
            "provided_fields": list(data.keys()) if data else []
        }), 400
    return None

@with_openvas_service
def create_target_controller(service):
    """Create a new target for scanning."""
    data = request.get_json()

    # Validate required fields
    validation_error = _validate_required_fields(data, ["name", "hosts"])
    if validation_error:
        return validation_error

    target_id = service.create_target(data["name"], data["hosts"])
    return jsonify({"target_id": target_id}), 201

@with_openvas_service
def get_resources_controller(service):
    """Get all available OpenVAS resources (targets, configs, scanners)."""
    targets = service.get_targets()
    configs = service.get_configs()
    scanners = service.get_scanners()

    return jsonify({
        "targets": targets,
        "configs": configs,
        "scanners": scanners
    }), 200

@with_openvas_service
def create_task_controller(service):
    """Create a new scanning task."""
    data = request.get_json()

    # Validate required fields
    required_fields = ["task_name", "target_id", "config_id", "scanner_id"]
    validation_error = _validate_required_fields(data, required_fields)
    if validation_error:
        return validation_error

    task_id = service.create_task(
        data["task_name"],
        data["target_id"],
        data["config_id"],
        data["scanner_id"]
    )
    return jsonify({"task_id": task_id}), 201

def start_task_controller(task_id):
    """
    Start a task and wait for completion (synchronous).
    Enhanced with detailed progress monitoring and error diagnostics.
    """
    service = _get_openvas_service()

    try:
        # 1. Verify task exists and get initial status
        print(f"[OpenVAS] Checking task {task_id} before starting...")
        initial_tasks = service.get_tasks()
        initial_task = next((t for t in initial_tasks if t["id"] == task_id), None)

        if not initial_task:
            return jsonify({"error": f"Task {task_id} not found"}), 404

        print(f"[OpenVAS] Task found: {initial_task.get('name')} (Status: {initial_task.get('status')})")

        # 2. Start the task
        print(f"[OpenVAS] Starting task {task_id}...")
        report_id = service.start_task(task_id)
        print(f"[OpenVAS] Task started with report_id: {report_id}")

        # 3. Wait a moment for task to initialize
        time.sleep(5)

        # 4. Check if task started properly
        check_tasks = service.get_tasks()
        check_task = next((t for t in check_tasks if t["id"] == task_id), None)

        if check_task:
            initial_status = check_task.get("status", "Unknown")
            print(f"[OpenVAS] Task status after start: {initial_status}")

            if initial_status in ["Stopped", "Interrupted"]:
                error_msg = f"Task failed to start properly. Status: {initial_status}. Check target accessibility, scanner availability, and task configuration."
                print(f"[OpenVAS] ERROR: {error_msg}")
                return jsonify({
                    "error": error_msg,
                    "task_id": task_id,
                    "report_id": report_id,
                    "status": initial_status,
                    "suggestions": [
                        "Verify target hosts are accessible",
                        "Check if scanner is available and running",
                        "Ensure scan configuration is valid",
                        "Check OpenVAS logs for detailed error information"
                    ]
                }), 400

        # 5. Poll until the task is completely finished (no timeout)
        poll_interval = 10  # check every 10 seconds
        last_progress = "0"
        consecutive_errors = 0
        max_consecutive_errors = 5

        print(f"[OpenVAS] Task {task_id} started successfully. Monitoring progress...")

        while True:
            try:
                tasks = service.get_tasks()
                task = next((t for t in tasks if t["id"] == task_id), None)

                if task:
                    progress = task.get("progress", "0")
                    status = task.get("status", "Unknown")

                    # Reset error counter on successful poll
                    consecutive_errors = 0

                    # Log progress changes
                    if progress != last_progress:
                        print(f"[OpenVAS] Task {task_id} progress: {progress}% (Status: {status})")
                        last_progress = progress

                    # Check if task is completely finished
                    if status == "Done" and progress == "100":
                        print(f"[OpenVAS] Task {task_id} completed successfully!")
                        break
                    elif status in ["Stopped", "Interrupted"]:
                        print(f"[OpenVAS] Task {task_id} was stopped or interrupted (Status: {status})")
                        # Don't break immediately, return detailed error info
                        final_tasks = service.get_tasks()
                        final_task = next((t for t in final_tasks if t["id"] == task_id), None)

                        return jsonify({
                            "error": f"Task was {status.lower()} during execution",
                            "task_id": task_id,
                            "report_id": report_id,
                            "final_status": status,
                            "final_progress": progress,
                            "suggestions": [
                                "Check OpenVAS scanner logs for detailed error information",
                                "Verify target hosts remain accessible during scan",
                                "Check if scan was manually stopped",
                                "Ensure sufficient system resources are available"
                            ]
                        }), 400
                else:
                    print(f"[OpenVAS] Warning: Task {task_id} not found in task list")
                    consecutive_errors += 1

            except Exception as poll_error:
                consecutive_errors += 1
                print(f"[OpenVAS] Error polling task status ({consecutive_errors}/{max_consecutive_errors}): {poll_error}")

                # If too many consecutive errors, abort
                if consecutive_errors >= max_consecutive_errors:
                    return jsonify({
                        "error": f"Too many consecutive polling errors. Last error: {str(poll_error)}",
                        "task_id": task_id,
                        "report_id": report_id
                    }), 500

            time.sleep(poll_interval)

        # Get final task status
        final_tasks = service.get_tasks()
        final_task = next((t for t in final_tasks if t["id"] == task_id), None)

        response_data = {
            "report_id": report_id,
            "task_id": task_id,
            "final_status": final_task.get("status") if final_task else "Unknown",
            "final_progress": final_task.get("progress") if final_task else "Unknown",
            "message": "Task completed successfully"
        }

        return jsonify(response_data), 200

    except Exception as e:
        error_msg = f"Failed to start or monitor task: {str(e)}"
        print(f"[OpenVAS] Error: {error_msg}")
        return jsonify({"error": error_msg}), 500

    finally:
        service.close()

@with_openvas_service
def start_task_async_controller(service, task_id):
    """Start a task asynchronously (returns immediately without waiting for completion)."""
    report_id = service.start_task(task_id)

    response_data = {
        "report_id": report_id,
        "task_id": task_id,
        "message": "Task started successfully (running asynchronously)",
        "note": "Use GET /openvas/tasks/{task_id}/status to check progress"
    }

    return jsonify(response_data), 200

@with_openvas_service
def get_task_status_controller(service, task_id):
    """Get the current status and progress of a specific task."""
    tasks = service.get_tasks()
    task = next((t for t in tasks if t["id"] == task_id), None)

    if not task:
        return jsonify({"error": f"Task {task_id} not found"}), 404

    response_data = {
        "task_id": task_id,
        "name": task.get("name", "Unknown"),
        "status": task.get("status", "Unknown"),
        "progress": task.get("progress", "0"),
        "is_complete": task.get("status") == "Done" and task.get("progress") == "100"
    }

    return jsonify(response_data), 200

@with_openvas_service
def diagnose_task_controller(service, task_id):
    """Diagnose why a task might be failing or getting interrupted."""
    # Get task details
    tasks = service.get_tasks()
    task = next((t for t in tasks if t["id"] == task_id), None)

    if not task:
        return jsonify({"error": f"Task {task_id} not found"}), 404

    # Get all related information
    targets = service.get_targets()
    configs = service.get_configs()
    scanners = service.get_scanners()
    reports = service.get_reports()

    # Find related target and reports
    task_target = None
    task_reports = []

    # This is a simplified approach - in a real implementation you'd need
    # to parse the task XML to get the actual IDs
    for target in targets:
        if target.get("name") in task.get("name", ""):
            task_target = target
            break

    for report in reports:
        if task_id in report.get("task_name", ""):
            task_reports.append(report)

    diagnostic_info = {
        "task": {
            "id": task.get("id"),
            "name": task.get("name"),
            "status": task.get("status"),
            "progress": task.get("progress")
        },
        "target": task_target,
        "available_configs": len(configs),
        "available_scanners": len(scanners),
        "related_reports": len(task_reports),
        "system_status": {
            "total_tasks": len(tasks),
            "running_tasks": len([t for t in tasks if t.get("status") == "Running"]),
            "done_tasks": len([t for t in tasks if t.get("status") == "Done"]),
            "interrupted_tasks": len([t for t in tasks if t.get("status") == "Interrupted"])
        },
        "recommendations": []
    }

    # Add recommendations based on status
    if task.get("status") == "Interrupted":
        diagnostic_info["recommendations"].extend([
            "Check if target hosts are accessible from OpenVAS scanner",
            "Verify scan configuration is appropriate for target",
            "Check OpenVAS scanner system resources (CPU, memory, disk)",
            "Review OpenVAS logs for detailed error messages",
            "Ensure no firewall is blocking the scan"
        ])
    elif task.get("status") == "New":
        diagnostic_info["recommendations"].extend([
            "Task has not been started yet",
            "Use POST /openvas/start/{task_id} to start the task"
        ])

    return jsonify(diagnostic_info), 200


@with_openvas_service
def get_reports_controller(service):
    """Get all available scan reports."""
    reports = service.get_reports()
    return jsonify(reports), 200

@with_openvas_service
def get_results_controller(service, report_id):
    """Get detailed results for a specific report."""
    results = service.get_results(report_id)
    return jsonify(results), 200

@with_openvas_service
def delete_task_controller(service, task_id):
    """Delete a specific task."""
    success = service.delete_task(task_id)
    return jsonify({"success": success}), 200 if success else 404
