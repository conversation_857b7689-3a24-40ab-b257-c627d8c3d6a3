from flask import request, jsonify
from app.utils.hash_utils import verify_password_custom
from app.extensions import mongo, mail
from flask_mail import Message
from flask_jwt_extended import create_access_token
from datetime import timedelta
import random

login_otp_store = {}

def send_login_otp(email, otp):
    msg = Message(
        subject="PICA - Login Verification Code",
        sender="<EMAIL>",
        recipients=[email],
        body=f"Your login verification code is: {otp}"
    )
    mail.send(msg)

def login_step1():
    data = request.get_json()
    email = data.get("email")
    password = data.get("password")

    user = mongo.db.users.find_one({"email": email})
    
    if user.get("banned"):
        return jsonify({"msg": "Your account has been banned"}), 403
    
    if not user.get("email_verified", False):
        return jsonify({"msg": "Please verify your email before logging in"}), 403

    if not user:
        return jsonify({"msg": "User not found"}), 404

    if not verify_password_custom(password, user["password"]):
        return jsonify({"msg": "Invalid credentials"}), 401

    otp = str(random.randint(100000, 999999))
    login_otp_store[email] = {
        "otp": otp,
        "user": user
    }

    send_login_otp(email, otp)
    return jsonify({ "msg": "OTP sent to email" }), 200

def verify_otp_login():
    data = request.get_json()
    email = data.get("email")
    otp = data.get("otp")

    if email not in login_otp_store:
        return jsonify({ "msg": "No login in progress" }), 400

    record = login_otp_store[email]
    if record["otp"] != otp:
        return jsonify({ "msg": "Invalid OTP" }), 400

    user = record["user"]
    del login_otp_store[email]

    token = create_access_token(
        identity=user["username"],
        additional_claims={
            "id": str(user["_id"]),
            "username": user["username"],
            "email": user["email"],
            "role": user.get("role", "user")
        },
        expires_delta=timedelta(hours=2)
    )

    return jsonify({
        "msg": "Login successful",
        "token": token,
        "id": str(user["_id"]),
        "username": user["username"],
        "email": user["email"]
    }), 200
