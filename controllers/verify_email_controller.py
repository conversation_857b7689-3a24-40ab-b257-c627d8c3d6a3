from flask import request, jsonify
from flask_jwt_extended import decode_token
from app.extensions import mongo

def confirm_email():
    token = request.args.get("token")

    if not token:
        return jsonify({"msg": "Missing token"}), 400

    try:
        decoded = decode_token(token)
    except Exception:
        return jsonify({"msg": "Invalid or expired confirmation link"}), 400

    if not decoded.get("verify"):
        return jsonify({"msg": "Invalid confirmation token"}), 403

    email = decoded["sub"]
    result = mongo.db.users.update_one(
        {"email": email},
        {"$set": {"email_verified": True}}
    )

    if result.modified_count == 0:
        return jsonify({"msg": "User not found or already verified"}), 404

    return jsonify({"msg": "Email verified. You can now log in."}), 200
