from flask import request, jsonify, current_app
from extensions import mongo, mail
from flask_jwt_extended import create_access_token, decode_token
from flask_mail import Message
from utils.helpers import (
    is_valid_name, is_valid_date, is_strong_password,
    is_valid_email, is_valid_username
)
from datetime import <PERSON><PERSON><PERSON>


def register():
    data = request.get_json()
    required = ["first_name", "last_name", "date_of_birth", "username", "gender", "email", "password"]
    if not all(k in data for k in required):
        return jsonify({"msg": "Missing fields"}), 400

    if not is_valid_name(data["first_name"]) or not is_valid_name(data["last_name"]):
        return jsonify({"msg": "Invalid name"}), 400
    if not is_valid_date(data["date_of_birth"]):
        return jsonify({"msg": "Date must be DD/MM/YYYY"}), 400
    if not is_valid_username(data["username"]):
        return jsonify({"msg": "Invalid username format"}), 400
    if not is_valid_email(data["email"]):
        return jsonify({"msg": "Invalid email format"}), 400
    if not is_strong_password(data["password"]):
        return jsonify({"msg": "Weak password"}), 400

    if mongo.db.users.find_one({"username": data["username"]}):
        return jsonify({"msg": "Username already taken"}), 400
    if mongo.db.users.find_one({"email": data["email"]}):
        return jsonify({"msg": "Email unavailable"}), 400

    data["role"] = "user"
    data["banned"] = False
    data["email_verified"] = False
    data["2fa_enabled"] = False

    user = current_app.user_datastore.create_user(**data, active=True)

    verify_token = create_access_token(
        identity=data["email"],
        expires_delta=timedelta(minutes=10),
        additional_claims={"verify": True}
    )

    confirm_url = f"http://127.0.0.1:5000/auth/confirm-email?token={verify_token}"
    msg = Message(
        subject="PICA – Confirm your email",
        sender="<EMAIL>",
        recipients=[data["email"]],
        body=(
            f"Hello {data['first_name']},\n\n"
            f"Please confirm your email by clicking the link below:\n\n"
            f"{confirm_url}\n\n"
            f"This link will expire in 10 minutes."
        )
    )
    mail.send(msg)

    return jsonify({"msg": "User registered. Please verify your email."}), 201


def confirm_email():
    token = request.args.get("token")
    if not token:
        return jsonify({"msg": "Missing token"}), 400

    try:
        decoded = decode_token(token)
    except Exception:
        return jsonify({"msg": "Invalid or expired confirmation link"}), 400

    if not decoded.get("verify"):
        return jsonify({"msg": "Invalid confirmation token"}), 403

    email = decoded["sub"]
    result = mongo.db.users.update_one(
        {"email": email},
        {"$set": {"email_verified": True}}
    )

    if result.modified_count == 0:
        return jsonify({"msg": "User not found or already verified"}), 404

    return jsonify({"msg": "Email verified. You can now log in."}), 200