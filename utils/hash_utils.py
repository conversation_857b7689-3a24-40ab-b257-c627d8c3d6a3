import hashlib
import base64
import hmac
import os

ITERATIONS = 2 ** 24
ALGO = "sha256"

# Charge le salt en base64 depuis .env
SALT_B64 = os.getenv("HASH_SALT", "cGljYTIwMjU=")  # fallback = "pica2025"
SALT = base64.b64decode(SALT_B64.encode("utf-8"))

def hash_password_custom(password: str) -> str:
    hash_bytes = hashlib.pbkdf2_hmac(
        ALGO,
        password.encode("utf-8"),
        SALT,
        ITERATIONS
    )
    return base64.b64encode(hash_bytes).decode("utf-8")

def verify_password_custom(password: str, hashed: str) -> bool:
    test_hash = hashlib.pbkdf2_hmac(
        ALGO,
        password.encode("utf-8"),
        SALT,
        ITERATIONS
    )
    return hmac.compare_digest(test_hash, base64.b64decode(hashed.encode("utf-8")))
