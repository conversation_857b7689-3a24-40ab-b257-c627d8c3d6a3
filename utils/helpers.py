import re
from datetime import datetime


def is_valid_name(name):
    return bool(re.fullmatch(r"[A-Za-z]+", name))

def is_valid_date(date_str):
    try:
        datetime.strptime(date_str, "%d/%m/%Y")
        return True
    except ValueError:
        return False

def is_strong_password(password):
    pattern = r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$"
    return bool(re.match(pattern, password))

def is_valid_email(email):
    return bool(re.match(r"^[\w\.-]+@[\w\.-]+\.\w+$", email))

def is_valid_username(username):
    return bool(re.fullmatch(r"[a-zA-Z0-9_.]{3,20}", username))
    return name.isalpha()

