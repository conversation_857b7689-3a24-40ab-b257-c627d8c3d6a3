"""
Scan Routes Module

Routes unifiées pour tous les outils de scan de vulnérabilités.
Centralise les endpoints pour OpenVAS, ZAP, Nikto et autres outils.
"""

from flask import Blueprint
from controllers.scan_controller import (
    # Endpoints génériques
    get_available_tools,

    # Endpoints OpenVAS
    get_openvas_port_lists,
    get_openvas_configs,
    create_openvas_target,
    start_openvas_scan,
    get_openvas_scan_status,
    get_openvas_scan_results,

    # Endpoints futurs
    # start_zap_scan,
    # start_nikto_scan
)

# Créer le blueprint pour les routes de scan
scan_bp = Blueprint('scan', __name__, url_prefix='/api/scan')

# ============================================================================
# ROUTES GÉNÉRIQUES
# ============================================================================

@scan_bp.route('/tools', methods=['GET'])
def route_get_available_tools():
    """GET /api/scan/tools - Obtenir la liste des outils de scan disponibles"""
    return get_available_tools()

# ============================================================================
# ROUTES OPENVAS
# ============================================================================

@scan_bp.route('/openvas/port-lists', methods=['GET'])
def route_get_openvas_port_lists():
    """GET /api/scan/openvas/port-lists - Récupérer les port lists OpenVAS"""
    return get_openvas_port_lists()

@scan_bp.route('/openvas/configs', methods=['GET'])
def route_get_openvas_configs():
    """GET /api/scan/openvas/configs - Récupérer les configurations OpenVAS"""
    return get_openvas_configs()

@scan_bp.route('/openvas/targets', methods=['POST'])
def route_create_openvas_target():
    """
    POST /api/scan/openvas/targets - Créer une cible OpenVAS

    Body JSON:
    {
        "name": "Ma cible",
        "hosts": "***********-10",
        "port_list_id": "33d0cd82-57c6-11e1-8ed1-406186ea4fc5"
    }
    """
    return create_openvas_target()

@scan_bp.route('/openvas/scan', methods=['POST'])
def route_start_openvas_scan():
    """
    POST /api/scan/openvas/scan - Démarrer un scan OpenVAS

    Body JSON:
    {
        "target_hosts": "***********-10",
        "scan_name": "Mon scan" (optionnel)
    }
    """
    return start_openvas_scan()

@scan_bp.route('/openvas/scan/<task_id>/status', methods=['GET'])
def route_get_openvas_scan_status(task_id):
    """GET /api/scan/openvas/scan/{task_id}/status - Statut d'un scan OpenVAS"""
    return get_openvas_scan_status(task_id)

@scan_bp.route('/openvas/scan/<task_id>/results', methods=['GET'])
def route_get_openvas_scan_results(task_id):
    """GET /api/scan/openvas/scan/{task_id}/results - Résultats d'un scan OpenVAS"""
    return get_openvas_scan_results(task_id)

# ============================================================================
# ROUTES FUTURES (ZAP, NIKTO, etc.)
# ============================================================================

# @scan_bp.route('/zap/scan', methods=['POST'])
# def route_start_zap_scan():
#     """POST /api/scan/zap/scan - Démarrer un scan ZAP"""
#     return start_zap_scan()

# @scan_bp.route('/nikto/scan', methods=['POST'])
# def route_start_nikto_scan():
#     """POST /api/scan/nikto/scan - Démarrer un scan Nikto"""
#     return start_nikto_scan()

# ============================================================================
# ROUTES D'ADMINISTRATION
# ============================================================================

@scan_bp.route('/health', methods=['GET'])
def route_health_check():
    """GET /api/scan/health - Vérification de santé des services de scan"""
    return get_available_tools()

# ============================================================================
# DOCUMENTATION DES ENDPOINTS
# ============================================================================

@scan_bp.route('/docs', methods=['GET'])
def route_api_documentation():
    """GET /api/scan/docs - Documentation des endpoints disponibles"""
    endpoints = {
        'generic': {
            'GET /api/scan/tools': 'Liste des outils de scan disponibles',
            'GET /api/scan/health': 'Vérification de santé des services'
        },
        'openvas': {
            'GET /api/scan/openvas/port-lists': 'Port lists disponibles',
            'GET /api/scan/openvas/configs': 'Configurations de scan',
            'POST /api/scan/openvas/targets': 'Créer une cible',
            'POST /api/scan/openvas/scan': 'Démarrer un scan',
            'GET /api/scan/openvas/scan/{task_id}/status': 'Statut du scan',
            'GET /api/scan/openvas/scan/{task_id}/results': 'Résultats du scan'
        },
        'future': {
            'POST /api/scan/zap/scan': 'Démarrer un scan ZAP (à venir)',
            'POST /api/scan/nikto/scan': 'Démarrer un scan Nikto (à venir)'
        }
    }

    return {
        'api_version': '1.0',
        'description': 'API unifiée pour les outils de scan de vulnérabilités',
        'base_url': '/api/scan',
        'endpoints': endpoints,
        'supported_tools': ['openvas'],
        'planned_tools': ['zap', 'nikto']
    }, 200

# ============================================================================
# GESTION D'ERREURS
# ============================================================================

@scan_bp.errorhandler(404)
def handle_not_found(error):
    """Gestionnaire d'erreur 404 pour les routes de scan"""
    return {
        'error': 'Endpoint not found',
        'message': 'The requested scan endpoint does not exist',
        'available_endpoints': '/api/scan/docs'
    }, 404

@scan_bp.errorhandler(500)
def handle_internal_error(error):
    """Gestionnaire d'erreur 500 pour les routes de scan"""
    return {
        'error': 'Internal server error',
        'message': 'An error occurred while processing the scan request',
        'support': 'Check logs for more details'
    }, 500
