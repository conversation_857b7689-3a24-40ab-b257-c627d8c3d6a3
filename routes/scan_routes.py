from flask import Blueprint
from app.controllers.nessus_controller import nessus_bp
from app.controllers.openvas_controller import (
    create_target_controller,
    get_resources_controller,
    create_task_controller,
    start_task_controller,
    get_reports_controller,
    get_results_controller,
    delete_task_controller
)

# Blueprint principal pour les routes liées aux scans
scan_bp = Blueprint("scan", __name__, url_prefix="/scan")

# ==========================
# Sous-routes Nessus
# ==========================
scan_bp.register_blueprint(nessus_bp, url_prefix="/nessus")

# ==========================
# Routes OpenVAS
# ==========================
scan_bp.route("/openvas/target", methods=["POST"])(create_target_controller)
scan_bp.route("/openvas/resources", methods=["GET"])(get_resources_controller)
scan_bp.route("/openvas/task", methods=["POST"])(create_task_controller)
scan_bp.route("/openvas/start/<task_id>", methods=["POST"])(start_task_controller)
scan_bp.route("/openvas/reports", methods=["GET"])(get_reports_controller)
scan_bp.route("/openvas/results/<report_id>", methods=["GET"])(get_results_controller)
scan_bp.route("/openvas/tasks/<task_id>", methods=["DELETE"])(delete_task_controller)
