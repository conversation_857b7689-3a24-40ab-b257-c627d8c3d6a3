from flask import Blueprint
from app.controllers.register_controller import register
from app.controllers.login_controller import signup
from app.controllers.protected_controller import protected
from app.controllers.login_2fa_controller import login_step1, verify_otp_login
from app.controllers.password_controller import send_reset_link, reset_password
from app.controllers.verify_email_controller import confirm_email


auth_bp = Blueprint("auth", __name__)

auth_bp.route("/register", methods=["POST"])(register)
#auth_bp.route("/signup", methods=["POST"])(signup)
auth_bp.route("/forgot-password", methods=["POST"])(send_reset_link)
auth_bp.route("/reset-password", methods=["POST"])(reset_password)
auth_bp.route("/protected", methods=["GET"])(protected)
auth_bp.route("/login-2fa", methods=["POST"])(login_step1)
auth_bp.route("/verify-otp-login", methods=["POST"])(verify_otp_login)
auth_bp.route("/confirm-email", methods=["GET"])(confirm_email)



