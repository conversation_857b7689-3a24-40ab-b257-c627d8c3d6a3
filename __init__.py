from flask import Flask
from .config import Config
from .extensions import mongo, jwt, mail, security
from routes.auth_routes import auth_bp
from routes.admin.admin_routes import admin_bp
from routes.scan_routes import scan_bp
from models.user_datastore_pymongo import PyMongoUserDatastore


def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # 1. Initialise Mongo
    mongo.init_app(app)

    # 2. Initialise les autres extensions
    jwt.init_app(app)
    mail.init_app(app)

    # 3. Configure Flask-Security avec la base Mongo
    user_datastore = PyMongoUserDatastore(mongo.cx.get_database("pica"))
    security.init_app(app, user_datastore)
    app.user_datastore = user_datastore  # Exposé pour utilisation ailleurs

    # 4. Enregistre les blueprints
    app.register_blueprint(auth_bp, url_prefix="/auth")
    app.register_blueprint(admin_bp, url_prefix="/admin")
    app.register_blueprint(scan_bp)  # contient Nessus + OpenVAS

    return app
