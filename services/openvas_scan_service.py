import requests
import xml.etree.ElementTree as ET
import base64
import uuid
import time
from datetime import datetime
from typing import Dict, List, Optional
import subprocess

class OpenVASService:
    
    def __init__(self, host='127.0.0.1', port=9392, username='admin', password='admin'):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.base_url = f"https://{host}:{port}"
        self.session_token = None
    
    def authenticate(self) -> bool:
        """S'authentifier auprès d'OpenVAS"""
        try:
            # Créer la requête d'authentification XML
            auth_xml = f"""
            <authenticate>
                <credentials>
                    <username>{self.username}</username>
                    <password>{self.password}</password>
                </credentials>
            </authenticate>
            """
            
            response = requests.post(
                f"{self.base_url}/gmp",
                data=auth_xml,
                headers={'Content-Type': 'application/xml'},
                verify=False,
                timeout=30
            )
            
            if response.status_code == 200:
                # Parser la réponse pour extraire le token
                root = ET.fromstring(response.text)
                token_elem = root.find('.//token')
                if token_elem is not None:
                    self.session_token = token_elem.text
                    return True
            
            return False
            
        except Exception as e:
            print(f"Erreur d'authentification OpenVAS: {e}")
            return False
    
    def create_target(self, name: str, hosts: str, port_list_id: str = None) -> str:
        """Créer une cible de scan"""
        try:
            if not self.session_token and not self.authenticate():
                return None
            
            target_id = str(uuid.uuid4())
            
            # Utiliser la liste de ports par défaut si non spécifiée
            if not port_list_id:
                port_list_id = self.get_default_port_list_id()
            
            create_target_xml = f"""
            <create_target token="{self.session_token}">
                <name>{name}</name>
                <hosts>{hosts}</hosts>
                <port_list id="{port_list_id}"/>
            </create_target>
            """
            
            response = requests.post(
                f"{self.base_url}/gmp",
                data=create_target_xml,
                headers={'Content-Type': 'application/xml'},
                verify=False,
                timeout=30
            )
            
            if response.status_code == 200:
                root = ET.fromstring(response.text)
                if root.get('status') == '201':
                    return root.get('id')
            
            return None
            
        except Exception as e:
            print(f"Erreur lors de la création de la cible: {e}")
            return None
    
    def get_default_port_list_id(self) -> str:
        """Obtenir l'ID de la liste de ports par défaut"""
        try:
            get_port_lists_xml = f"""
            <get_port_lists token="{self.session_token}"/>
            """
            
            response = requests.post(
                f"{self.base_url}/gmp",
                data=get_port_lists_xml,
                headers={'Content-Type': 'application/xml'},
                verify=False,
                timeout=30
            )
            
            if response.status_code == 200:
                root = ET.fromstring(response.text)
                # Chercher la liste "All IANA assigned TCP and UDP"
                for port_list in root.findall('.//port_list'):
                    name_elem = port_list.find('name')
                    if name_elem is not None and 'All IANA' in name_elem.text:
                        return port_list.get('id')
                
                # Fallback: prendre la première liste disponible
                first_list = root.find('.//port_list')
                if first_list is not None:
                    return first_list.get('id')
            
            return "33d0cd82-57c6-11e1-8ed1-406186ea4fc5"  # ID par défaut
            
        except Exception as e:
            print(f"Erreur lors de la récupération des listes de ports: {e}")
            return "33d0cd82-57c6-11e1-8ed1-406186ea4fc5"
    
    def get_scan_configs(self) -> List[Dict]:
        """Obtenir les configurations de scan disponibles"""
        try:
            if not self.session_token and not self.authenticate():
                return []
            
            get_configs_xml = f"""
            <get_configs token="{self.session_token}"/>
            """
            
            response = requests.post(
                f"{self.base_url}/gmp",
                data=get_configs_xml,
                headers={'Content-Type': 'application/xml'},
                verify=False,
                timeout=30
            )
            
            configs = []
            if response.status_code == 200:
                root = ET.fromstring(response.text)
                for config in root.findall('.//config'):
                    name_elem = config.find('name')
                    if name_elem is not None:
                        configs.append({
                            'id': config.get('id'),
                            'name': name_elem.text,
                            'comment': config.find('comment').text if config.find('comment') is not None else ''
                        })
            
            return configs
            
        except Exception as e:
            print(f"Erreur lors de la récupération des configurations: {e}")
            return []
    
    def create_task(self, name: str, target_id: str, config_id: str = None) -> str:
        """Créer une tâche de scan"""
        try:
            if not self.session_token and not self.authenticate():
                return None
            
            # Utiliser la configuration par défaut si non spécifiée
            if not config_id:
                configs = self.get_scan_configs()
                if configs:
                    config_id = configs[0]['id']  # Prendre la première config
            
            create_task_xml = f"""
            <create_task token="{self.session_token}">
                <name>{name}</name>
                <config id="{config_id}"/>
                <target id="{target_id}"/>
            </create_task>
            """
            
            response = requests.post(
                f"{self.base_url}/gmp",
                data=create_task_xml,
                headers={'Content-Type': 'application/xml'},
                verify=False,
                timeout=30
            )
            
            if response.status_code == 200:
                root = ET.fromstring(response.text)
                if root.get('status') == '201':
                    return root.get('id')
            
            return None
            
        except Exception as e:
            print(f"Erreur lors de la création de la tâche: {e}")
            return None
    
    def start_task(self, task_id: str) -> bool:
        """Démarrer une tâche de scan"""
        try:
            if not self.session_token and not self.authenticate():
                return False
            
            start_task_xml = f"""
            <start_task token="{self.session_token}" task_id="{task_id}"/>
            """
            
            response = requests.post(
                f"{self.base_url}/gmp",
                data=start_task_xml,
                headers={'Content-Type': 'application/xml'},
                verify=False,
                timeout=30
            )
            
            if response.status_code == 200:
                root = ET.fromstring(response.text)
                return root.get('status') == '202'
            
            return False
            
        except Exception as e:
            print(f"Erreur lors du démarrage de la tâche: {e}")
            return False
    
    def get_task_status(self, task_id: str) -> Dict:
        """Obtenir le statut d'une tâche"""
        try:
            if not self.session_token and not self.authenticate():
                return {'status': 'error', 'message': 'Authentication failed'}
            
            get_task_xml = f"""
            <get_tasks token="{self.session_token}" task_id="{task_id}"/>
            """
            
            response = requests.post(
                f"{self.base_url}/gmp",
                data=get_task_xml,
                headers={'Content-Type': 'application/xml'},
                verify=False,
                timeout=30
            )
            
            if response.status_code == 200:
                root = ET.fromstring(response.text)
                task = root.find('.//task')
                if task is not None:
                    status_elem = task.find('status')
                    progress_elem = task.find('progress')
                    
                    return {
                        'status': status_elem.text if status_elem is not None else 'Unknown',
                        'progress': int(progress_elem.text) if progress_elem is not None else 0
                    }
            
            return {'status': 'error', 'message': 'Task not found'}
            
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def scan_target(self, target_hosts: str, scan_name: str = None) -> Dict:
        """Lancer un scan complet sur une cible"""
        scan_id = str(uuid.uuid4())
        
        if not scan_name:
            scan_name = f"Scan_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            start_time = datetime.utcnow()
            
            # 1. Créer la cible
            target_id = self.create_target(f"Target_{scan_id}", target_hosts)
            if not target_id:
                return {'status': 'error', 'message': 'Failed to create target'}
            
            # 2. Créer la tâche
            task_id = self.create_task(scan_name, target_id)
            if not task_id:
                return {'status': 'error', 'message': 'Failed to create task'}
            
            # 3. Démarrer le scan
            if not self.start_task(task_id):
                return {'status': 'error', 'message': 'Failed to start task'}
            
            # 4. Attendre la fin du scan (avec timeout)
            timeout = 3600  # 1 heure
            start_wait = time.time()
            
            while time.time() - start_wait < timeout:
                task_status = self.get_task_status(task_id)
                
                if task_status['status'] == 'Done':
                    break
                elif task_status['status'] in ['Stopped', 'Interrupted']:
                    return {
                        'scan_id': scan_id,
                        'status': 'failed',
                        'message': f"Scan {task_status['status'].lower()}"
                    }
                
                time.sleep(30)  # Vérifier toutes les 30 secondes
            
            end_time = datetime.utcnow()
            
            # 5. Récupérer les résultats
            vulnerabilities = self.get_scan_results(task_id)
            
            return {
                'scan_id': scan_id,
                'task_id': task_id,
                'target_id': target_id,
                'target_hosts': target_hosts,
                'status': 'completed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': (end_time - start_time).total_seconds(),
                'vulnerabilities': vulnerabilities,
                'total_vulnerabilities': len(vulnerabilities)
            }
            
        except Exception as e:
            return {
                'scan_id': scan_id,
                'status': 'error',
                'error': str(e)
            }
    
    def get_scan_results(self, task_id: str) -> List[Dict]:
        """Récupérer les résultats d'un scan"""
        try:
            if not self.session_token and not self.authenticate():
                return []
            
            get_results_xml = f"""
            <get_results token="{self.session_token}" task_id="{task_id}"/>
            """
            
            response = requests.post(
                f"{self.base_url}/gmp",
                data=get_results_xml,
                headers={'Content-Type': 'application/xml'},
                verify=False,
                timeout=60
            )
            
            vulnerabilities = []
            if response.status_code == 200:
                root = ET.fromstring(response.text)
                
                for result in root.findall('.//result'):
                    nvt = result.find('nvt')
                    host = result.find('host')
                    
                    if nvt is not None and host is not None:
                        vuln = {
                            'id': result.get('id'),
                            'name': nvt.find('name').text if nvt.find('name') is not None else '',
                            'description': result.find('description').text if result.find('description') is not None else '',
                            'severity': float(result.find('severity').text) if result.find('severity') is not None else 0.0,
                            'threat': result.find('threat').text if result.find('threat') is not None else 'Log',
                            'host': host.text,
                            'port': result.find('port').text if result.find('port') is not None else '',
                            'nvt_oid': nvt.get('oid'),
                            'cvss_base': nvt.find('cvss_base').text if nvt.find('cvss_base') is not None else '',
                            'cve': [ref.get('id') for ref in nvt.findall('.//ref[@type="cve"]')],
                            'category': 'vulnerability',
                            'tool': 'openvas'
                        }
                        vulnerabilities.append(vuln)
            
            return vulnerabilities
            
        except Exception as e:
            print(f"Erreur lors de la récupération des résultats: {e}")
            return []
    
    def get_status(self) -> Dict:
        """Obtenir le statut du service OpenVAS"""
        return {
            'service': 'openvas',
            'available': self.is_available(),
            'version': self.get_version(),
            'host': self.host,
            'port': self.port,
            'authenticated': bool(self.session_token)
        }
