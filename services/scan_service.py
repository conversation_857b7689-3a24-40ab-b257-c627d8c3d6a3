"""
Service unifié de gestion des scans de vulnérabilités

Ce service orchestre les différents outils de scan (OpenVAS, Nessus, etc.)
et fournit une interface unifiée pour l'ensemble de l'application.
"""

from typing import Dict, List, Optional, Union
from datetime import datetime, timezone
import logging
from enum import Enum

from .openvas_scan_service import OpenVASService
from .nessus_api import NessusAPI

logger = logging.getLogger(__name__)

class ScanTool(Enum):
    """Énumération des outils de scan supportés"""
    OPENVAS = "openvas"
    NESSUS = "nessus"

class ScanStatus(Enum):
    """États possibles d'un scan"""
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class UnifiedScanService:
    """
    Service unifié pour la gestion des scans de vulnérabilités
    
    Ce service :
    - Orchestre les différents outils de scan
    - Fournit une interface unifiée
    - Gère la configuration et le routage des scans
    - Normalise les résultats entre les différents outils
    """
    
    def __init__(self):
        self.openvas_service = None
        self.nessus_service = None
        self._initialize_services()
    
    def _initialize_services(self):
        """Initialiser les services de scan disponibles"""
        try:
            # Initialiser OpenVAS
            self.openvas_service = OpenVASService()
            logger.info("OpenVAS service initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize OpenVAS service: {e}")
        
        try:
            # Initialiser Nessus (à adapter selon votre configuration)
            # self.nessus_service = NessusAPI()
            logger.info("Nessus service initialization skipped (not configured)")
        except Exception as e:
            logger.warning(f"Failed to initialize Nessus service: {e}")
    
    def get_available_tools(self) -> List[Dict]:
        """Obtenir la liste des outils de scan disponibles"""
        tools = []
        
        if self.openvas_service and self.openvas_service.is_available():
            tools.append({
                'name': ScanTool.OPENVAS.value,
                'display_name': 'OpenVAS',
                'status': self.openvas_service.get_status(),
                'available': True
            })
        
        if self.nessus_service:
            tools.append({
                'name': ScanTool.NESSUS.value,
                'display_name': 'Nessus',
                'status': {},  # À implémenter
                'available': True
            })
        
        return tools
    
    def create_target(self, tool: str, name: str, hosts: str, **kwargs) -> Optional[str]:
        """
        Créer une cible de scan avec l'outil spécifié
        
        Args:
            tool: Nom de l'outil (openvas, nessus)
            name: Nom de la cible
            hosts: Hôtes à scanner
            **kwargs: Paramètres spécifiques à l'outil
        
        Returns:
            ID de la cible créée ou None en cas d'erreur
        """
        try:
            if tool == ScanTool.OPENVAS.value and self.openvas_service:
                return self.openvas_service.create_target(
                    name=name,
                    hosts=hosts,
                    port_list_id=kwargs.get('port_list_id')
                )
            elif tool == ScanTool.NESSUS.value and self.nessus_service:
                # À implémenter selon l'API Nessus
                pass
            else:
                logger.error(f"Tool {tool} not available or not supported")
                return None
                
        except Exception as e:
            logger.error(f"Error creating target with {tool}: {e}")
            return None
    
    def start_scan(self, tool: str, target_hosts: str, scan_name: str = None, **kwargs) -> Dict:
        """
        Démarrer un scan avec l'outil spécifié
        
        Args:
            tool: Nom de l'outil
            target_hosts: Hôtes à scanner
            scan_name: Nom du scan (optionnel)
            **kwargs: Paramètres spécifiques à l'outil
        
        Returns:
            Dictionnaire avec les informations du scan
        """
        if not scan_name:
            timestamp = datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')
            scan_name = f"Scan_{tool}_{timestamp}"
        
        try:
            if tool == ScanTool.OPENVAS.value and self.openvas_service:
                return self.openvas_service.scan_target(
                    target_hosts=target_hosts,
                    scan_name=scan_name
                )
            elif tool == ScanTool.NESSUS.value and self.nessus_service:
                # À implémenter selon l'API Nessus
                return {
                    'status': 'error',
                    'message': 'Nessus integration not yet implemented'
                }
            else:
                return {
                    'status': 'error',
                    'message': f'Tool {tool} not available'
                }
                
        except Exception as e:
            logger.error(f"Error starting scan with {tool}: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def get_scan_status(self, tool: str, scan_id: str) -> Dict:
        """
        Obtenir le statut d'un scan
        
        Args:
            tool: Nom de l'outil
            scan_id: ID du scan
        
        Returns:
            Statut du scan
        """
        try:
            if tool == ScanTool.OPENVAS.value and self.openvas_service:
                return self.openvas_service.get_task_status(scan_id)
            elif tool == ScanTool.NESSUS.value and self.nessus_service:
                # À implémenter
                pass
            else:
                return {
                    'status': 'error',
                    'message': f'Tool {tool} not available'
                }
                
        except Exception as e:
            logger.error(f"Error getting scan status from {tool}: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def get_scan_results(self, tool: str, scan_id: str) -> List[Dict]:
        """
        Récupérer les résultats d'un scan
        
        Args:
            tool: Nom de l'outil
            scan_id: ID du scan
        
        Returns:
            Liste des vulnérabilités trouvées
        """
        try:
            if tool == ScanTool.OPENVAS.value and self.openvas_service:
                return self.openvas_service.get_scan_results(scan_id)
            elif tool == ScanTool.NESSUS.value and self.nessus_service:
                # À implémenter
                return []
            else:
                logger.error(f"Tool {tool} not available")
                return []
                
        except Exception as e:
            logger.error(f"Error getting scan results from {tool}: {e}")
            return []
    
    def normalize_vulnerability(self, vuln: Dict, tool: str) -> Dict:
        """
        Normaliser une vulnérabilité selon un format unifié
        
        Args:
            vuln: Vulnérabilité brute de l'outil
            tool: Outil source
        
        Returns:
            Vulnérabilité normalisée
        """
        # Format unifié pour toutes les vulnérabilités
        normalized = {
            'id': vuln.get('id', ''),
            'name': vuln.get('name', ''),
            'description': vuln.get('description', ''),
            'severity': self._normalize_severity(vuln.get('severity', 0), tool),
            'cvss_score': vuln.get('cvss_base', 0),
            'host': vuln.get('host', ''),
            'port': vuln.get('port', ''),
            'cve_ids': vuln.get('cve', []),
            'tool': tool,
            'category': vuln.get('category', 'vulnerability'),
            'threat_level': vuln.get('threat', 'Unknown'),
            'solution': vuln.get('solution', ''),
            'references': vuln.get('references', [])
        }
        
        return normalized
    
    def _normalize_severity(self, severity: Union[str, float], tool: str) -> str:
        """Normaliser le niveau de sévérité"""
        if tool == ScanTool.OPENVAS.value:
            if isinstance(severity, (int, float)):
                if severity >= 7.0:
                    return "High"
                elif severity >= 4.0:
                    return "Medium"
                elif severity > 0.0:
                    return "Low"
                else:
                    return "Info"
        
        # Fallback pour les autres outils
        if isinstance(severity, str):
            return severity.capitalize()
        
        return "Unknown"
