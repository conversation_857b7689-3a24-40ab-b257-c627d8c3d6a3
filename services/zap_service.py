import requests
import json
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional
import subprocess
import os

class ZAPService:
    """Service pour l'intégration avec OWASP ZAP"""
    
    def __init__(self, zap_host='127.0.0.1', zap_port=8080, api_key=None):
        self.zap_host = zap_host
        self.zap_port = zap_port
        self.api_key = api_key
        self.base_url = f"http://{zap_host}:{zap_port}"
        self.zap_path = self._find_zap_path()
    
    def _find_zap_path(self) -> str:
        """Trouver le chemin vers ZAP"""
        possible_paths = [
            '/usr/share/zaproxy/zap.sh',
            '/opt/zaproxy/zap.sh',
            '/usr/local/bin/zap.sh',
            'zap.sh'
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        return 'zap.sh'  # Fallback
    
    def is_available(self) -> bool:
        """Vérifier si ZAP est disponible et en cours d'exécution"""
        try:
            response = requests.get(f"{self.base_url}/JSON/core/view/version/", timeout=5)
            return response.status_code == 200
        except requests.RequestException:
            return False
    
    def start_zap_daemon(self) -> Dict:
        """Démarrer ZAP en mode daemon"""
        try:
            cmd = [
                self.zap_path,
                '-daemon',
                '-host', self.zap_host,
                '-port', str(self.zap_port),
                '-config', 'api.disablekey=true'
            ]
            
            if self.api_key:
                cmd.extend(['-config', f'api.key={self.api_key}'])
            
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Attendre que ZAP soit prêt
            for _ in range(30):  # Attendre jusqu'à 30 secondes
                if self.is_available():
                    return {'status': 'started', 'pid': process.pid}
                time.sleep(1)
            
            return {'status': 'timeout', 'message': 'ZAP failed to start within 30 seconds'}
            
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def spider_scan(self, target_url: str, max_children: int = 10) -> Dict:
        """Lancer un spider scan sur une URL"""
        scan_id = str(uuid.uuid4())
        
        try:
            # Démarrer le spider
            params = {
                'url': target_url,
                'maxChildren': max_children,
                'recurse': 'true'
            }
            
            if self.api_key:
                params['apikey'] = self.api_key
            
            response = requests.get(f"{self.base_url}/JSON/spider/action/scan/", params=params)
            
            if response.status_code != 200:
                return {'status': 'error', 'message': 'Failed to start spider scan'}
            
            spider_id = response.json()['scan']
            
            # Attendre la fin du scan
            start_time = datetime.utcnow()
            while True:
                status_response = requests.get(
                    f"{self.base_url}/JSON/spider/view/status/",
                    params={'scanId': spider_id, 'apikey': self.api_key} if self.api_key else {'scanId': spider_id}
                )
                
                if status_response.status_code == 200:
                    status = int(status_response.json()['status'])
                    if status >= 100:
                        break
                
                time.sleep(2)
            
            end_time = datetime.utcnow()
            
            # Récupérer les résultats
            results_response = requests.get(
                f"{self.base_url}/JSON/spider/view/results/",
                params={'scanId': spider_id, 'apikey': self.api_key} if self.api_key else {'scanId': spider_id}
            )
            
            urls_found = []
            if results_response.status_code == 200:
                urls_found = results_response.json()['results']
            
            return {
                'scan_id': scan_id,
                'spider_id': spider_id,
                'target_url': target_url,
                'status': 'completed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': (end_time - start_time).total_seconds(),
                'urls_found': urls_found,
                'total_urls': len(urls_found)
            }
            
        except Exception as e:
            return {
                'scan_id': scan_id,
                'target_url': target_url,
                'status': 'error',
                'error': str(e)
            }
    
    def active_scan(self, target_url: str, policy_name: str = None) -> Dict:
        """Lancer un scan actif sur une URL"""
        scan_id = str(uuid.uuid4())
        
        try:
            # Paramètres du scan actif
            params = {'url': target_url}
            
            if policy_name:
                params['scanPolicyName'] = policy_name
            
            if self.api_key:
                params['apikey'] = self.api_key
            
            # Démarrer le scan actif
            response = requests.get(f"{self.base_url}/JSON/ascan/action/scan/", params=params)
            
            if response.status_code != 200:
                return {'status': 'error', 'message': 'Failed to start active scan'}
            
            ascan_id = response.json()['scan']
            
            # Attendre la fin du scan
            start_time = datetime.utcnow()
            while True:
                status_response = requests.get(
                    f"{self.base_url}/JSON/ascan/view/status/",
                    params={'scanId': ascan_id, 'apikey': self.api_key} if self.api_key else {'scanId': ascan_id}
                )
                
                if status_response.status_code == 200:
                    status = int(status_response.json()['status'])
                    if status >= 100:
                        break
                
                time.sleep(5)  # Scan actif plus long
            
            end_time = datetime.utcnow()
            
            # Récupérer les alertes (vulnérabilités)
            alerts = self.get_alerts(target_url)
            
            return {
                'scan_id': scan_id,
                'ascan_id': ascan_id,
                'target_url': target_url,
                'status': 'completed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': (end_time - start_time).total_seconds(),
                'vulnerabilities': alerts,
                'total_vulnerabilities': len(alerts)
            }
            
        except Exception as e:
            return {
                'scan_id': scan_id,
                'target_url': target_url,
                'status': 'error',
                'error': str(e)
            }
    
    def get_alerts(self, base_url: str = None) -> List[Dict]:
        """Récupérer les alertes de sécurité"""
        try:
            params = {}
            if base_url:
                params['baseurl'] = base_url
            if self.api_key:
                params['apikey'] = self.api_key
            
            response = requests.get(f"{self.base_url}/JSON/core/view/alerts/", params=params)
            
            if response.status_code != 200:
                return []
            
            alerts_data = response.json()['alerts']
            vulnerabilities = []
            
            for alert in alerts_data:
                vuln = {
                    'id': alert.get('id', ''),
                    'name': alert.get('name', ''),
                    'description': alert.get('description', ''),
                    'risk': alert.get('risk', 'Low'),
                    'confidence': alert.get('confidence', 'Low'),
                    'url': alert.get('url', ''),
                    'param': alert.get('param', ''),
                    'attack': alert.get('attack', ''),
                    'evidence': alert.get('evidence', ''),
                    'solution': alert.get('solution', ''),
                    'reference': alert.get('reference', ''),
                    'cweid': alert.get('cweid', ''),
                    'wascid': alert.get('wascid', ''),
                    'severity': self._map_risk_to_severity(alert.get('risk', 'Low')),
                    'category': 'web_vulnerability',
                    'tool': 'owasp_zap'
                }
                vulnerabilities.append(vuln)
            
            return vulnerabilities
            
        except Exception as e:
            print(f"Erreur lors de la récupération des alertes ZAP: {e}")
            return []
    
    def _map_risk_to_severity(self, risk: str) -> str:
        """Mapper le niveau de risque ZAP vers notre système de sévérité"""
        risk_mapping = {
            'High': 'high',
            'Medium': 'medium',
            'Low': 'low',
            'Informational': 'info'
        }
        return risk_mapping.get(risk, 'low')
    
    def full_scan(self, target_url: str) -> Dict:
        """Effectuer un scan complet (spider + scan actif)"""
        scan_id = str(uuid.uuid4())
        start_time = datetime.utcnow()
        
        try:
            # 1. Spider scan
            spider_result = self.spider_scan(target_url)
            if spider_result['status'] != 'completed':
                return spider_result
            
            # 2. Active scan
            active_result = self.active_scan(target_url)
            if active_result['status'] != 'completed':
                return active_result
            
            end_time = datetime.utcnow()
            
            return {
                'scan_id': scan_id,
                'target_url': target_url,
                'status': 'completed',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': (end_time - start_time).total_seconds(),
                'spider_results': spider_result,
                'active_scan_results': active_result,
                'total_urls_found': spider_result.get('total_urls', 0),
                'total_vulnerabilities': active_result.get('total_vulnerabilities', 0)
            }
            
        except Exception as e:
            return {
                'scan_id': scan_id,
                'target_url': target_url,
                'status': 'error',
                'error': str(e)
            }
    
    def get_scan_policies(self) -> List[Dict]:
        """Obtenir les politiques de scan disponibles"""
        try:
            params = {}
            if self.api_key:
                params['apikey'] = self.api_key
            
            response = requests.get(f"{self.base_url}/JSON/ascan/view/scanPolicies/", params=params)
            
            if response.status_code == 200:
                return response.json()['scanPolicies']
            
        except Exception as e:
            print(f"Erreur lors de la récupération des politiques: {e}")
        
        return ['Default Policy']
    
    def generate_report(self, format_type: str = 'HTML') -> str:
        """Générer un rapport dans le format spécifié"""
        try:
            params = {'format': format_type}
            if self.api_key:
                params['apikey'] = self.api_key
            
            response = requests.get(f"{self.base_url}/OTHER/core/other/htmlreport/", params=params)
            
            if response.status_code == 200:
                return response.text
            
        except Exception as e:
            print(f"Erreur lors de la génération du rapport: {e}")
        
        return ""
    
    def get_status(self) -> Dict:
        """Obtenir le statut du service ZAP"""
        try:
            if self.is_available():
                version_response = requests.get(f"{self.base_url}/JSON/core/view/version/")
                version = version_response.json()['version'] if version_response.status_code == 200 else 'Unknown'
                
                return {
                    'service': 'owasp_zap',
                    'available': True,
                    'version': version,
                    'host': self.zap_host,
                    'port': self.zap_port,
                    'api_key_configured': bool(self.api_key)
                }
            else:
                return {
                    'service': 'owasp_zap',
                    'available': False,
                    'message': 'ZAP is not running or not accessible'
                }
                
        except Exception as e:
            return {
                'service': 'owasp_zap',
                'available': False,
                'error': str(e)
            }
