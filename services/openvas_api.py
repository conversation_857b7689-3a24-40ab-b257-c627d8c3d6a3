### app/services/openvas_api.py

import socket
import xml.etree.ElementTree as ET
import logging

logger = logging.getLogger("OpenVASAPI")
logging.basicConfig(level=logging.INFO)


class OpenVASAPI:
    def __init__(self, username, password, socket_path="/var/run/gvmd/gvmd.sock"):
        self.username = username
        self.password = password
        self.socket_path = socket_path
        self.socket = None
        self._connect()
        self._authenticate()

    def _connect(self):
        self.socket = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        self.socket.connect(self.socket_path)
        logger.info("Connected to GVM socket")

    def _send_command(self, xml):
        self.socket.send(xml.encode())
        response = b""
        while True:
            chunk = self.socket.recv(4096)
            response += chunk
            if len(chunk) < 4096:
                break
        return ET.fromstring(response.decode())

    def _authenticate(self):
        xml = f"""
        <authenticate>
            <credentials>
                <username>{self.username}</username>
                <password>{self.password}</password>
            </credentials>
        </authenticate>
        """
        root = self._send_command(xml)
        if root.attrib.get("status") != "200":
            raise Exception("Authentication failed")
        logger.info("Authenticated")

    def create_target(self, name, hosts, port_list_id="33d0cd82-57c6-11e1-8ed1-406186ea4fc5"):
        xml = f"""
        <create_target>
            <name>{name}</name>
            <hosts>{hosts}</hosts>
            <port_list id="{port_list_id}"/>
        </create_target>
        """
        root = self._send_command(xml)
        if root.attrib.get("status") == "201":
            return root.attrib["id"]
        raise Exception("Failed to create target")

    def get_targets(self):
        root = self._send_command("<get_targets/>")
        return [
            {"id": t.attrib["id"], "name": t.find("name").text, "hosts": t.find("hosts").text}
            for t in root.findall(".//target")
        ]

    def get_configs(self):
        root = self._send_command("<get_configs/>")
        return [{"id": c.attrib["id"], "name": c.find("name").text} for c in root.findall(".//config")]

    def get_scanners(self):
        root = self._send_command("<get_scanners/>")
        return [{"id": s.attrib["id"], "name": s.find("name").text} for s in root.findall(".//scanner")]

    def create_task(self, name, target_id, config_id, scanner_id):
        xml = f"""
        <create_task>
            <name>{name}</name>
            <config id="{config_id}"/>
            <target id="{target_id}"/>
            <scanner id="{scanner_id}"/>
        </create_task>
        """
        root = self._send_command(xml)
        if root.attrib.get("status") == "201":
            return root.attrib["id"]
        raise Exception("Failed to create task")

    def start_task(self, task_id):
        xml = f"""<start_task task_id=\"{task_id}\"/>"""
        root = self._send_command(xml)
        if root.attrib.get("status") == "202":
            return root.find(".//report_id").text
        raise Exception("Failed to start task")

    def get_reports(self):
        root = self._send_command("<get_reports/>")
        return [
            {
                "id": r.attrib["id"],
                "task_name": r.find(".//task/name").text if r.find(".//task/name") is not None else "Unknown",
                "created": r.find(".//creation_time").text if r.find(".//creation_time") is not None else ""
            }
            for r in root.findall(".//report") if "id" in r.attrib
        ]

    def get_results(self, report_id):
        xml = f"""<get_reports report_id=\"{report_id}\"/>"""
        root = self._send_command(xml)
        results = []
        for res in root.findall(".//result"):
            name = res.find("name").text if res.find("name") is not None else ""
            severity = res.find("severity").text if res.find("severity") is not None else ""
            port = res.find("port").text if res.find("port") is not None else ""
            results.append({"name": name, "severity": severity, "port": port})
        return results

    def delete_task(self, task_id):
        xml = f"""<delete_task task_id=\"{task_id}\"/>"""
        root = self._send_command(xml)
        return root.attrib.get("status") == "200"

    def close(self):
        if self.socket:
            self.socket.close()
            self.socket = None

    def get_tasks(self):
        root = self._send_command("<get_tasks/>")
        return [
            {
                "id": t.attrib["id"],
                "name": t.find("name").text,
                "status": t.find("status").text,
                "progress": t.find("progress").text if t.find("progress") is not None else "-"
            }
            for t in root.findall(".//task")
        ]

