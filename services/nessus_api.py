import requests
import json
from urllib3.exceptions import InsecureRequestWarning
import urllib3
urllib3.disable_warnings(InsecureRequestWarning)


class NessusAPI:
    def __init__(self, base_url, access_key, secret_key, verify_ssl=False):
        self.base_url = base_url.rstrip('/')
        self.access_key = access_key
        self.secret_key = secret_key
        self.verify_ssl = verify_ssl
        self.headers = {
            'Content-Type': 'application/json',
            'X-ApiKeys': f'accessKey={self.access_key}; secretKey={self.secret_key}'
        }
        self.session = requests.Session()

    def _request(self, method, endpoint, params=None, data=None):
        url = f"{self.base_url}{endpoint}"
        try:
            if data is not None and not isinstance(data, str):
                data = json.dumps(data)

            response = self.session.request(
                method=method,
                url=url,
                headers=self.headers,
                params=params,
                data=data,
                verify=self.verify_ssl
            )
            response.raise_for_status()

            if response.content:
                try:
                    return response.json()
                except ValueError:
                    return {"raw": response.text}
            return {"status": "success"}

        except requests.exceptions.RequestException as e:
            error = f"{e}"
            if hasattr(e, 'response') and e.response is not None:
                error += f" - {e.response.text}"
            raise Exception(f"Nessus API Error: {error}")

    def list_scans(self):
        """
        Liste tous les scans disponibles.
        """
        return self._request("GET", "/scans")

    def get_or_create_folder(self, name):
        """
        Retourne l'ID du dossier s'il existe, sinon le crée.
        """
        folders = self._request("GET", "/folders").get("folders", [])
        for folder in folders:
            if folder["name"].lower() == name.lower():
                return folder["id"]
        response = self._request("POST", "/folders", data={"name": name})
        return response.get("id")


    def create_scan(self, name, targets, policy_id=None, folder_id=None, description=None, template_uuid=None):
        payload = {
            "uuid": template_uuid or "731a8e52-3ea6-a291-ec0a-d2ff0619c19d7bd788d6be818b65",  # fallback
            "settings": {
                "name": name,
                "text_targets": targets,
                "folder_id": folder_id,
                "description": description or ""
            }
        }

        if policy_id:
            payload["settings"]["policy_id"] = policy_id

        return self._request("POST", "/scans", data=payload)

    def launch_scan(self, scan_id):
        """
        Lance un scan existant.
        """
        return self._request("POST", f"/scans/{scan_id}/launch")

    def get_scan_details(self, scan_id):
        """
        Récupère les détails d'un scan spécifique.
        """
        return self._request("GET", f"/scans/{scan_id}")

    def delete_scan(self, scan_id):
        """
        Supprime un scan.
        """
        return self._request("DELETE", f"/scans/{scan_id}")

    def export_scan(self, scan_id, format="nessus"):
        """
        Prépare l'export d'un rapport (PDF, HTML, CSV, Nessus).
        """
        payload = {"format": format}
        return self._request("POST", f"/scans/{scan_id}/export", data=payload)

    def download_export(self, scan_id, file_id):
        """
        Télécharge le rapport exporté.
        """
        return self._request("GET", f"/scans/{scan_id}/export/{file_id}/download")

    def export_status(self, scan_id, file_id):
        """
        Vérifie si le fichier exporté est prêt.
        """
        return self._request("GET", f"/scans/{scan_id}/export/{file_id}/status")

    def get_plugin_details(self, plugin_id):
        """
        Récupère les détails d'un plugin spécifique.
        """
        return self._request("GET", f"/plugins/plugin/{plugin_id}")

    def list_plugin_families(self):
        """
        Liste des familles de plugins.
        """
        return self._request("GET", "/plugins/families")

    def get_family_plugins(self, family_id):
        """
        Récupère les plugins d'une famille spécifique.
        """
        return self._request("GET", f"/plugins/families/{family_id}")

    def create_policy(self, policy_data):
        """
        Crée une nouvelle politique de scan.
        """
        return self._request("POST", "/policies", data=policy_data)
