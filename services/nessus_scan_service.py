from app.services.nessus_api import NessusAPI
from flask import current_app

class NessusScanService:
    def __init__(self):
        self.api = NessusAPI(
            base_url=current_app.config["NESSUS_URL"],
            access_key=current_app.config["NESSUS_ACCESS_KEY"],
            secret_key=current_app.config["NESSUS_SECRET_KEY"],
            verify_ssl=current_app.config["NESSUS_VERIFY_SSL"]
        )

    def list_policies(self):
        return self.api._request("GET", "/policies")

    def get_or_create_folder(self, name):
        return self.api.get_or_create_folder(name)


    def list_scans(self):
        return self.api.list_scans()

    def list_policies(self):
        return self.api._request("GET", "/policies")

    def list_templates(self):
        return self.api._request("GET", "/editor/scan/templates")

    def create_scan(self, payload):
        """
        Create a scan using the provided payload.
        Sends the payload directly to the Nessus API without modification.
        """
        try:
            # Send the payload directly to the Nessus API
            return self.api._request("POST", "/scans", data=payload)
        except Exception as e:
            return {"error": f"Failed to create scan: {str(e)}"}

    def launch_scan(self, scan_id):
        """
        Launch an existing scan by its ID.
        """
        try:
            return self.api.launch_scan(scan_id)
        except Exception as e:
            return {"error": f"Failed to launch scan: {str(e)}"}

    def get_scan_details(self, scan_id):
        """
        Get details of a specific scan.
        """
        try:
            return self.api.get_scan_details(scan_id)
        except Exception as e:
            return {"error": f"Failed to get scan details: {str(e)}"}

    def delete_scan(self, scan_id):
        """
        Delete a scan.
        """
        try:
            return self.api.delete_scan(scan_id)
        except Exception as e:
            return {"error": f"Failed to delete scan: {str(e)}"}

    def export_scan(self, scan_id, format="nessus"):
        """
        Prepare scan report export.
        """
        try:
            return self.api.export_scan(scan_id, format)
        except Exception as e:
            return {"error": f"Failed to export scan: {str(e)}"}

    def download_export(self, scan_id, file_id):
        """
        Download exported report.
        """
        try:
            return self.api.download_export(scan_id, file_id)
        except Exception as e:
            return {"error": f"Failed to download export: {str(e)}"}

    def export_status(self, scan_id, file_id):
        """
        Check export status.
        """
        try:
            return self.api.export_status(scan_id, file_id)
        except Exception as e:
            return {"error": f"Failed to check export status: {str(e)}"}

    def get_plugin_details(self, plugin_id):
        """
        Get details of a specific plugin.
        """
        try:
            return self.api.get_plugin_details(plugin_id)
        except Exception as e:
            return {"error": f"Failed to get plugin details: {str(e)}"}

    def list_plugin_families(self):
        """
        List all plugin families.
        """
        try:
            return self.api.list_plugin_families()
        except Exception as e:
            return {"error": f"Failed to list plugin families: {str(e)}"}

    def get_family_plugins(self, family_id):
        """
        Get plugins from a specific family.
        """
        try:
            return self.api.get_family_plugins(family_id)
        except Exception as e:
            return {"error": f"Failed to get family plugins: {str(e)}"}

    def create_policy(self, policy_data):
        """
        Create a new scan policy.
        """
        try:
            return self.api.create_policy(policy_data)
        except Exception as e:
            return {"error": f"Failed to create policy: {str(e)}"}